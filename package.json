{"name": "vwork-platform", "version": "1.0.0", "description": "VWork - Comprehensive Freelancing Platform", "main": "scripts/unified-start.js", "scripts": {"start": "node scripts/unified-start.js", "dev": "node scripts/unified-start.js", "setup": "node scripts/setup-all.js", "stop": "node scripts/stop-new.js", "status": "node scripts/status.js", "test": "node scripts/test-api.js", "test:setup": "node scripts/test-setup.js", "test:firebase": "node scripts/check-firebase.js", "test:auth": "node scripts/test-auth.js", "deploy:render": "node scripts/deploy-render-manual.js", "setup:render": "node scripts/setup-render-env.js", "build": "node scripts/build-microservices.js all", "build:client": "cd client && npm run build", "build:production": "node scripts/build-microservices.js all --production", "build:service": "node scripts/build-microservices.js", "deploy:service": "node scripts/deploy-microservice.js", "install:deps": "node scripts/install-dependencies.js", "fix:axios": "node scripts/fix-axios-error.js", "test:simple": "node scripts/test-services-simple.js"}, "keywords": ["freelancing", "platform", "microservices", "react", "nodejs", "firebase"], "author": "VWork Team", "license": "MIT", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/VinkRasengan/Vwork.git"}, "bugs": {"url": "https://github.com/VinkRasengan/Vwork/issues"}, "homepage": "https://github.com/VinkRasengan/Vwork#readme", "devDependencies": {"cross-env": "^7.0.3", "eslint": "^8.54.0", "rimraf": "^5.0.5"}, "dependencies": {"axios": "^1.6.0", "dotenv": "^16.6.1"}}