import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, USER_TYPES } from '../contexts/AuthContext';
import { toast } from 'react-hot-toast';

const LoginSuccessPage = () => {
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  useEffect(() => {
    console.log('🔍 LoginSuccess: Checking user status...', { user, loading });

    // Wait for auth to be ready
    if (loading) {
      console.log('🔄 Auth still loading, waiting...');
      return;
    }

    // If no user, redirect to login
    if (!user) {
      console.log('❌ No user found, redirecting to login');
      toast.error('<PERSON>ên đăng nhập không hợp lệ');
      navigate('/auth?mode=signin');
      return;
    }

    console.log('👤 User found:', {
      email: user.email,
      userType: user.userType,
      profileComplete: user.profile?.isComplete,
      emailVerified: user.emailVerified
    });

    // Check if email is verified
    if (!user.emailVerified) {
      console.log('📧 Email not verified, redirecting to verification');
      navigate('/verify-email');
      return;
    }

    // Check if profile is complete
    if (user.profile?.isComplete === false) {
      console.log('📋 Profile incomplete, redirecting to appropriate setup');
      
      if (user.userType === USER_TYPES.FREELANCER) {
        console.log('➡️ Redirecting freelancer to profile setup');
        navigate('/profile-setup');
      } else if (user.userType === USER_TYPES.CLIENT) {
        console.log('➡️ Redirecting client to client setup');
        navigate('/client-setup');
      } else {
        console.log('⚠️ Unknown user type, redirecting to dashboard');
        navigate('/dashboard');
      }
    } else {
      console.log('✅ Profile complete, redirecting to dashboard');
      navigate('/dashboard');
    }
  }, [user, loading, navigate]);

  // Show loading spinner while determining redirect
  return (
    <div className="min-h-screen bg-gradient-to-br from-medieval-brown-50 to-medieval-red-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <div className="bg-white rounded-2xl shadow-xl border border-medieval-brown-200 p-8 text-center">
          {/* Logo */}
          <div className="mx-auto w-16 h-16 bg-medieval-red-600 rounded-full flex items-center justify-center mb-4">
            <span className="text-white font-cinzel-decorative text-xl font-bold">V</span>
          </div>
          
          <h1 className="font-cinzel-decorative text-xl font-bold text-medieval-brown-800 mb-4">
            VWork Guild
          </h1>
          
          {/* Loading state */}
          <div className="flex flex-col items-center space-y-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-medieval-red-600"></div>
            <p className="font-cinzel text-medieval-brown-600">
              Đang kiểm tra thông tin tài khoản...
            </p>
          </div>
          
          {/* Debug info in development */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-6 p-4 bg-gray-100 rounded-lg text-left text-xs">
              <p><strong>Debug Info:</strong></p>
              <p>Loading: {loading ? 'true' : 'false'}</p>
              <p>User: {user ? 'exists' : 'null'}</p>
              {user && (
                <>
                  <p>Email: {user.email}</p>
                  <p>Type: {user.userType}</p>
                  <p>Profile Complete: {user.profile?.isComplete?.toString()}</p>
                  <p>Email Verified: {user.emailVerified?.toString()}</p>
                </>
              )}
            </div>
          )}

          {/* Production debug info */}
          {process.env.NODE_ENV === 'production' && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg text-left text-xs">
              <p><strong>Production Debug:</strong></p>
              <p>Environment: {process.env.NODE_ENV}</p>
              <p>Firebase API Key: {process.env.REACT_APP_FIREBASE_API_KEY ? 'Set' : 'Missing'}</p>
              <p>Firebase Auth Domain: {process.env.REACT_APP_FIREBASE_AUTH_DOMAIN ? 'Set' : 'Missing'}</p>
              <p>Current Path: {window.location.pathname}</p>
              <p>User Authenticated: {user ? 'Yes' : 'No'}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default LoginSuccessPage;
