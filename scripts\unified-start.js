#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

// Store all spawned processes for proper cleanup
let spawnedProcesses = [];
const pidsFile = path.join(__dirname, '..', '.vwork-pids.json');
const isWindows = os.platform() === 'win32';

// Enhanced cleanup function
function cleanup() {
  console.log('\n🧹 Cleaning up all processes...');
  
  // Kill all spawned processes
  spawnedProcesses.forEach((proc, index) => {
    try {
      if (proc && proc.pid && !proc.killed) {
        console.log(`Stopping process ${index + 1} (PID: ${proc.pid})`);
        if (isWindows) {
          exec(`taskkill /F /T /PID ${proc.pid}`, () => {});
        } else {
          process.kill(-proc.pid); // Kill process group
        }
      }
    } catch (error) {
      console.log(`Could not kill process ${index + 1}:`, error.message);
    }
  });

  // Save PIDs to file for stop script
  const allPids = spawnedProcesses.map(proc => proc.pid).filter(pid => pid);
  try {
    fs.writeFileSync(pidsFile, JSON.stringify(allPids, null, 2));
    console.log('💾 Saved process PIDs for cleanup');
  } catch (error) {
    console.log('⚠️  Could not save PID file:', error.message);
  }
  
  // Kill additional processes that might be running
  killAllVworkProcesses();
  
  setTimeout(() => {
    process.exit(0);
  }, 2000);
}

// Kill all VWork related processes
function killAllVworkProcesses() {
  const processesToKill = [
    'node.exe', 'npm.exe', 'npx.exe', 'react-scripts',
    'node', 'npm', 'npx'
  ];
  
  const portsToKill = [3000, 3001, 3002, 3003, 3004, 3005, 8080, 5000, 8000];
  
  console.log('🔄 Killing all VWork processes...');
  
  // Kill by process name
  processesToKill.forEach(processName => {
    if (isWindows) {
      exec(`taskkill /F /IM ${processName} /T 2>nul`, () => {});
    } else {
      exec(`pkill -f ${processName} 2>/dev/null`, () => {});
    }
  });
  
  // Kill by port
  portsToKill.forEach(port => {
    if (isWindows) {
      exec(`netstat -ano | findstr :${port} | for /f "tokens=5" %a in ('findstr :${port}') do taskkill /F /PID %a 2>nul`, () => {});
    } else {
      exec(`lsof -ti:${port} | xargs kill -9 2>/dev/null`, () => {});
    }
  });
}

// Handle process termination signals
process.on('SIGINT', cleanup);
process.on('SIGTERM', cleanup);
process.on('exit', cleanup);

// Load environment variables
const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  require('dotenv').config({ path: envPath });
  console.log('✅ Environment variables loaded from .env');
} else {
  console.log('⚠️  No .env file found in root directory');
}

// Enhanced port cleanup
async function killProcessesOnPorts(ports = [3000, 3001, 3002, 3003, 3004, 3005, 8080, 5000, 8000]) {
  console.log('🔍 Checking for processes on ports:', ports.join(', '));
  
  for (const port of ports) {
    try {
      console.log(`   Checking port ${port}...`);
      
      let command;
      if (isWindows) {
        command = `netstat -ano | findstr :${port}`;
      } else {
        command = `lsof -ti:${port}`;
      }
      
      const result = await new Promise((resolve) => {
        exec(command, { timeout: 5000 }, (error, stdout, stderr) => {
          if (error) {
            resolve(null);
          } else {
            resolve(stdout.trim());
          }
        });
      });
      
      if (result && result.length > 0) {
        console.log(`⚠️  Found process on port ${port}, killing...`);
        
        if (isWindows) {
          const lines = result.split('\n');
          const pidsToKill = new Set();
          
          for (const line of lines) {
            const parts = line.trim().split(/\s+/);
            if (parts.length > 4) {
              const pid = parts[parts.length - 1];
              if (pid && !isNaN(pid) && pid !== '0') {
                pidsToKill.add(pid);
              }
            }
          }
          
          for (const pid of pidsToKill) {
            console.log(`     Killing PID ${pid}...`);
            await new Promise((resolve) => {
              exec(`taskkill /F /T /PID ${pid}`, { timeout: 3000 }, () => resolve());
            });
          }
        } else {
          const pids = result.split('\n').filter(pid => pid && !isNaN(pid));
          for (const pid of pids) {
            console.log(`     Killing PID ${pid}...`);
            await new Promise((resolve) => {
              exec(`kill -9 ${pid}`, { timeout: 3000 }, () => resolve());
            });
          }
        }
        
        // Wait a bit for processes to die
        await new Promise(resolve => setTimeout(resolve, 1000));
        console.log(`✅ Cleaned port ${port}`);
      } else {
        console.log(`✅ Port ${port} is free`);
      }
    } catch (error) {
      console.log(`⚠️  Error checking port ${port}: ${error.message}`);
    }
  }
  
  // Extra cleanup step - use kill-port as backup
  console.log('🧹 Running additional port cleanup...');
  for (const port of ports) {
    try {
      await new Promise((resolve) => {
        exec(`npx kill-port ${port}`, { timeout: 5000 }, () => resolve());
      });
    } catch (error) {
      // Ignore errors from kill-port
    }
  }
}

// Check and create environment files
async function setupEnvironmentFiles() {
  const rootEnvPath = path.join(__dirname, '..', '.env');
  const clientDir = path.join(__dirname, '..', 'client');
  const servicesDir = path.join(__dirname, '..', 'services');
  
  // Create client .env file
  const clientEnvPath = path.join(clientDir, '.env');
  let clientEnvContent = `# Client Environment Configuration
PORT=3000
REACT_APP_API_URL=http://localhost:8080
NODE_ENV=development
`;

  if (fs.existsSync(rootEnvPath)) {
    const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
    const clientEnvLines = rootEnvContent.split('\n')
      .filter(line => {
        const trimmed = line.trim();
        return trimmed.startsWith('REACT_APP_') || 
               trimmed.startsWith('NODE_ENV') ||
               trimmed.startsWith('#') ||
               trimmed === '';
      });
    clientEnvContent += '\n' + clientEnvLines.join('\n');
  }

  fs.writeFileSync(clientEnvPath, clientEnvContent);
  console.log('✅ Client .env file created');

  // Create services .env files
  const services = [
    { name: 'auth-service', port: 3001 },
    { name: 'user-service', port: 3002 },
    { name: 'project-service', port: 3003 },
    { name: 'payment-service', port: 3004 },
    { name: 'chat-service', port: 3005 },
    { name: 'api-gateway', port: 8080 }
  ];

  for (const service of services) {
    const serviceDir = path.join(servicesDir, service.name);
    const serviceEnvPath = path.join(serviceDir, '.env');
    
    if (fs.existsSync(serviceDir)) {
      let serviceEnvContent = `NODE_ENV=development
PORT=${service.port}
CORS_ORIGIN=http://localhost:3000
`;
      
      if (fs.existsSync(rootEnvPath)) {
        const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
        serviceEnvContent += '\n' + rootEnvContent;
      }
      
      fs.writeFileSync(serviceEnvPath, serviceEnvContent);
      console.log(`✅ ${service.name} .env file created`);
    }
  }
}

// Check dependencies
async function checkDependencies() {
  console.log('📦 Checking dependencies...');
  
  const directories = [
    { name: 'client', path: path.join(__dirname, '..', 'client') },
    { name: 'auth-service', path: path.join(__dirname, '..', 'services', 'auth-service') },
    { name: 'user-service', path: path.join(__dirname, '..', 'services', 'user-service') },
    { name: 'project-service', path: path.join(__dirname, '..', 'services', 'project-service') },
    { name: 'payment-service', path: path.join(__dirname, '..', 'services', 'payment-service') },
    { name: 'chat-service', path: path.join(__dirname, '..', 'services', 'chat-service') },
    { name: 'api-gateway', path: path.join(__dirname, '..', 'services', 'api-gateway') }
  ];

  for (const dir of directories) {
    if (fs.existsSync(dir.path) && !fs.existsSync(path.join(dir.path, 'node_modules'))) {
      console.log(`📦 Installing ${dir.name} dependencies...`);
      await runCommand('npm install', dir.path);
    }
  }
}

// Run command utility
function runCommand(command, cwd) {
  return new Promise((resolve, reject) => {
    const childProcess = exec(command, { cwd }, (error) => {
      if (error) {
        console.error(`Error in ${cwd}: ${error.message}`);
        reject(error);
      } else {
        resolve();
      }
    });
    
    childProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    childProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
  });
}

// Start service function
function startService(serviceName, servicePath, command, port) {
  return new Promise((resolve, reject) => {
    console.log(`🚀 Starting ${serviceName} on port ${port}...`);
    
    const childProcess = spawn(command, [], {
      cwd: servicePath,
      stdio: 'pipe',
      shell: true,
      env: { ...process.env, PORT: port.toString() }
    });
    
    // Store process for cleanup
    spawnedProcesses.push(childProcess);
    
    // Handle output
    childProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('error') || output.includes('Error')) {
        console.log(`❌ ${serviceName}: ${output.trim()}`);
      } else {
        console.log(`✅ ${serviceName}: ${output.trim()}`);
      }
    });
    
    childProcess.stderr.on('data', (data) => {
      const output = data.toString();
      if (!output.includes('warning')) {
        console.log(`⚠️  ${serviceName}: ${output.trim()}`);
      }
    });
    
    // Wait for service to be ready
    let ready = false;
    const checkReady = () => {
      if (ready) return;
      
      const checkPort = isWindows ? 
        `netstat -an | findstr :${port}` :
        `lsof -i :${port}`;
      
      exec(checkPort, (error, stdout) => {
        if (stdout && !ready) {
          ready = true;
          console.log(`✅ ${serviceName} is ready on port ${port}`);
          resolve();
        } else if (!ready) {
          setTimeout(checkReady, 1000);
        }
      });
    };
    
    // Start checking after a delay
    setTimeout(checkReady, 2000);
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!ready) {
        console.log(`⚠️  ${serviceName} startup timeout, but continuing...`);
        resolve();
      }
    }, 30000);
    
    childProcess.on('error', (error) => {
      console.error(`❌ Failed to start ${serviceName}:`, error.message);
      reject(error);
    });
  });
}

// Main function
async function startAll() {
  console.log('🚀 Starting VWork Platform...\n');
  
  try {
    // Clean up existing processes first
    console.log('🧹 Cleaning up existing processes...');
    await killProcessesOnPorts();
    
    // Setup environment
    console.log('\n⚙️  Setting up environment...');
    await setupEnvironmentFiles();
    
    // Check dependencies
    console.log('\n📦 Checking dependencies...');
    await checkDependencies();
    
    // Start services in order
    console.log('\n🚀 Starting services...');
    
    const services = [
      { name: 'API Gateway', path: path.join(__dirname, '..', 'services', 'api-gateway'), command: 'npm start', port: 8080 },
      { name: 'Auth Service', path: path.join(__dirname, '..', 'services', 'auth-service'), command: 'npm start', port: 3001 },
      { name: 'User Service', path: path.join(__dirname, '..', 'services', 'user-service'), command: 'npm start', port: 3002 },
      { name: 'Project Service', path: path.join(__dirname, '..', 'services', 'project-service'), command: 'npm start', port: 3003 },
      { name: 'Payment Service', path: path.join(__dirname, '..', 'services', 'payment-service'), command: 'npm start', port: 3004 },
      { name: 'Chat Service', path: path.join(__dirname, '..', 'services', 'chat-service'), command: 'npm start', port: 3005 }
    ];
    
    // Start services in parallel
    const servicePromises = services.map(service => 
      startService(service.name, service.path, service.command, service.port)
    );
    
    await Promise.all(servicePromises);
    
    // Start client after services
    console.log('\n🌐 Starting React client...');
    await startService('React Client', path.join(__dirname, '..', 'client'), 'npm start', 3000);
    
    console.log('\n🎉 All services started successfully!');
    console.log('📱 Client: http://localhost:3000');
    console.log('🔗 API Gateway: http://localhost:8080');
    console.log('🔐 Auth Service: http://localhost:3001');
    console.log('👤 User Service: http://localhost:3002');
    console.log('📋 Project Service: http://localhost:3003');
    console.log('💳 Payment Service: http://localhost:3004');
    console.log('💬 Chat Service: http://localhost:3005');
    console.log('\n💡 Press Ctrl+C to stop all services');
    
  } catch (error) {
    console.error('❌ Error starting services:', error.message);
    cleanup();
    process.exit(1);
  }
}

// Start the application
startAll();
