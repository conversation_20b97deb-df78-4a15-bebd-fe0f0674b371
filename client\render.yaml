services:
  - type: web
    runtime: static
    name: vwork-client
    region: oregon
    branch: main
    rootDir: client
    buildCommand: npm ci && npm run build
    publishDir: build
    envVars:
      - key: NODE_ENV
        value: production
      - key: GENERATE_SOURCEMAP
        value: false
      - key: REACT_APP_API_BASE_URL
        value: https://vwork-gateway.onrender.com/api/v1
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: 1050922072615
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02
    plan: free 