services:
  - type: web
    runtime: static
    name: vwork-client
    region: oregon
    branch: main
    rootDir: client
    buildCommand: npm ci && npm run build
    staticPublishPath: build
    # SPA routing configuration - redirect all routes to index.html
    routes:
      - type: rewrite
        source: "/*"
        destination: "/index.html"
    # Security and caching headers
    headers:
      - path: "/*"
        name: "X-Content-Type-Options"
        value: "nosniff"
      - path: "/*"
        name: "X-Frame-Options"
        value: "DENY"
      - path: "/*"
        name: "X-XSS-Protection"
        value: "1; mode=block"
      - path: "/*"
        name: "Referrer-Policy"
        value: "strict-origin-when-cross-origin"
      - path: "/static/*"
        name: "Cache-Control"
        value: "public, max-age=********, immutable"
      - path: "/*.html"
        name: "Cache-Control"
        value: "no-cache, no-store, must-revalidate"
    envVars:
      - key: NODE_ENV
        value: production
      - key: GENERATE_SOURCEMAP
        value: "false"
      - key: REACT_APP_API_URL
        value: https://vwork-gateway.onrender.com
      - key: REACT_APP_FIREBASE_API_KEY
        value: AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
      - key: REACT_APP_FIREBASE_AUTH_DOMAIN
        value: vwork-786c3.firebaseapp.com
      - key: REACT_APP_FIREBASE_PROJECT_ID
        value: vwork-786c3
      - key: REACT_APP_FIREBASE_STORAGE_BUCKET
        value: vwork-786c3.firebasestorage.app
      - key: REACT_APP_FIREBASE_MESSAGING_SENDER_ID
        value: "1050922072615"
      - key: REACT_APP_FIREBASE_APP_ID
        value: 1:1050922072615:web:dfeae89c9ba66c77aeec02
    plan: free