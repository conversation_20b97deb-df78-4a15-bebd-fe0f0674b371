# Handle client-side routing for all routes
/*    /index.html   200

# Specific routes for better performance and SEO
/    /index.html   200
/auth    /index.html   200
/auth?mode=signup    /index.html   200
/auth?mode=login    /index.html   200
/login    /index.html   200
/register    /index.html   200
/login-success    /index.html   200
/dashboard    /index.html   200
/profile-setup    /index.html   200
/client-setup    /index.html   200
/verify-email    /index.html   200
/messages    /index.html   200
/settings    /index.html   200
/forgot-password    /index.html   200
/reset-password    /index.html   200
/check-email    /index.html   200
/simple-login-success    /index.html   200
/projects    /index.html   200
/projects/*    /index.html   200
/freelancers    /index.html   200
/freelancers/*    /index.html   200
/jobs    /index.html   200
/contests    /index.html   200
/community    /index.html   200
/support    /index.html   200

# API routes should not be redirected (if any static API routes exist)
/api/*    /api/:splat   200

# Static assets should be served directly
/static/*    /static/:splat   200
/images/*    /images/:splat   200
/css/*       /css/:splat      200
/js/*        /js/:splat       200
