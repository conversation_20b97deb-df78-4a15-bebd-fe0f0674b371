# 🚀 VWork - Freelancer Platform

A modern microservices-based freelancer platform built with React, Node.js, and Firebase.

## 📋 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Git

### Installation
```bash
# Clone repository
git clone https://github.com/VinkRasengan/Vwork.git
cd Vwork

# Install dependencies
npm install

# Start development environment
npm start
```

### Development URLs
- **Frontend**: http://localhost:3000
- **API Gateway**: http://localhost:8080
- **Auth Service**: http://localhost:3001
- **User Service**: http://localhost:3002
- **Project Service**: http://localhost:3003
- **Job Service**: http://localhost:3004
- **Chat Service**: http://localhost:3005

## 🏗️ Architecture

### Microservices Structure
```
VWork Platform
├── API Gateway (Port 8080) - Main entry point
├── Auth Service (Port 3001) - Firebase authentication
├── User Service (Port 3002) - User management
├── Project Service (Port 3003) - Project management
├── Job Service (Port 3004) - Job postings
├── Chat Service (Port 3005) - Real-time messaging
└── React Client (Port 3000) - Frontend application
```

### Technology Stack
- **Frontend**: React, Tailwind CSS, Firebase Auth
- **Backend**: Node.js, Express.js, Firebase Admin SDK
- **Database**: Firebase Firestore
- **Deployment**: Render.com
- **CI/CD**: GitHub Actions

## 🚀 Deployment

### Production Deployment
```bash
# Deploy to Render
npm run deploy:prepare
npm run deploy:env
git push origin main
```

### Manual Deployment
Follow the deployment guide in `DEPLOYMENT_CHECKLIST.md`

## 📚 Documentation

- **Deployment Guide**: `DEPLOYMENT_CHECKLIST.md`
- **API Documentation**: `services/API_SPECIFICATION.md`
- **Architecture Review**: `MICROSERVICES_ARCHITECTURE_REVIEW.md`

## 🛠️ Development

### Available Scripts
```bash
npm start              # Start all services
npm stop               # Stop all services
npm run build          # Build all services
npm test               # Run tests
npm run deploy:prepare # Prepare for deployment
npm run deploy:env     # Generate environment variables
```

### Service Development
```bash
# Start specific service
cd services/auth-service && npm run dev

# Build specific service
npm run build:service auth-service
```

## 🔧 Configuration

### Environment Variables
Create `.env` file in root directory:
```env
NODE_ENV=development
FIREBASE_PROJECT_ID=your-project-id
JWT_SECRET=your-jwt-secret
```

### Firebase Setup
1. Create Firebase project
2. Enable Authentication and Firestore
3. Download service account key
4. Update environment variables

## 📊 Monitoring

### Health Checks
All services provide health endpoints:
- `/health` - Service status
- `/health/detailed` - Detailed health information

### Logs
- Development: Console output
- Production: Render dashboard logs

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Test thoroughly
5. Submit pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check existing documentation
2. Review service logs
3. Create GitHub issue

---

**VWork Platform** - Empowering freelancers and clients worldwide! 🌟
