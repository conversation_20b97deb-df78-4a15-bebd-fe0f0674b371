import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { useEffect } from 'react';

console.log('🔥 App.js is loading...');

// Import contexts
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { ThemeProvider } from './contexts/ThemeContext';

// Import components
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import ProfileGuard from './components/auth/ProfileGuard';

// Import pages
import HomePage from './pages/HomePage';
import AppleAuthPage from './components/apple/pages/AppleAuthPage';
import AppleDashboard from './components/apple/pages/AppleDashboard';
import AppleProjectsPage from './components/apple/pages/AppleProjectsPage';
import AppleFreelancersPage from './components/apple/pages/AppleFreelancersPage';
import AppleJobsPage from './components/apple/pages/AppleJobsPage';
import AppleJobCreatePage from './components/apple/pages/AppleJobCreatePage';
import AppleJobDetailPage from './components/apple/pages/AppleJobDetailPage';
import AppleJobApplyPage from './components/apple/pages/AppleJobApplyPage';
import AppleContestsPage from './components/apple/pages/AppleContestsPage';
import AppleCommunityPage from './components/apple/pages/AppleCommunityPage';
import ForgotPasswordPage from './pages/ForgotPasswordPage';
import EmailVerificationPage from './pages/EmailVerificationPage';
import PasswordResetPage from './pages/PasswordResetPage';
import ProfileSetupPage from './pages/ProfileSetupPage';
import ClientSetupPage from './pages/ClientSetupPage';
import MessagesPage from './pages/MessagesPage';
import SettingsPage from './pages/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';
import EmailVerificationModal from './components/common/EmailVerificationModal';
import LoginSuccessPage from './pages/LoginSuccessPage';
import SimpleLoginSuccess from './pages/SimpleLoginSuccess';
import EmailVerificationCheck from './pages/EmailVerificationCheck';
import FreelancerProfilePage from './pages/FreelancerProfilePage';
import ProjectDetailPage from './pages/ProjectDetailPage';
import SupportPage from './pages/SupportPage';

// Import utilities
import performanceMonitor from './utils/performanceMonitor';
import { debugRouting, checkCreateAccountButton } from './utils/routingDebug';

// Debug component for development only
const DebugComponent = () => {
  if (process.env.NODE_ENV === 'development') {
    try {
      const UserDebug = require('./components/debug/UserDebug').default;
      return <UserDebug />;
    } catch (error) {
      console.warn('UserDebug component not found in production build');
      return null;
    }
  }
  return null;
};

// App component with email verification modal
const AppContent = () => {
  const { 
    showEmailVerification, 
    verificationEmail, 
    handleResendVerification, 
    handleContinueVerification 
  } = useAuth();

  return (
    <>
      <Router basename={process.env.NODE_ENV === 'production' ? '' : undefined}>
        <Layout>
          <ProfileGuard>
            <Routes>
              {/* Public Routes */}
              <Route path='/' element={<HomePage />} />
              <Route path='/projects' element={<AppleProjectsPage />} />
              <Route path='/projects/:id' element={<ProjectDetailPage />} />
              <Route path='/freelancers' element={<AppleFreelancersPage />} />
              <Route path='/freelancers/:id' element={<FreelancerProfilePage />} />
              <Route path='/jobs' element={<AppleJobsPage />} />
              <Route path='/contests' element={<AppleContestsPage />} />
              <Route path='/community' element={<AppleCommunityPage />} />
              <Route path='/support' element={<SupportPage />} />

              {/* Auth Routes - redirect if already authenticated */}
              <Route
                path='/auth'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <AppleAuthPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path='/login'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <AppleAuthPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path='/register'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <AppleAuthPage />
                  </ProtectedRoute>
                }
              />
              
              {/* Login Success Handler */}
              <Route
                path='/login-success'
                element={
                  <ProtectedRoute requireAuth={true}>
                    <LoginSuccessPage />
                  </ProtectedRoute>
                }
              />
              
              <Route
                path='/forgot-password'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <ForgotPasswordPage />
                  </ProtectedRoute>
                }
              />

              {/* Email Verification */}
              <Route
                path='/verify-email'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <EmailVerificationPage />
                  </ProtectedRoute>
                }
              />

              {/* Password Reset */}
              <Route
                path='/reset-password'
                element={
                  <ProtectedRoute requireAuth={false}>
                    <PasswordResetPage />
                  </ProtectedRoute>
                }
              />

              {/* Profile Setup - for freelancers after registration */}
              <Route
                path='/profile-setup'
                element={
                  <ProtectedRoute>
                    <ProfileSetupPage />
                  </ProtectedRoute>
                }
              />

              {/* Client Setup - for clients after registration */}
              <Route
                path='/client-setup'
                element={
                  <ProtectedRoute>
                    <ClientSetupPage />
                  </ProtectedRoute>
                }
              />

              {/* Simple login success for testing */}
              <Route
                path='/simple-login-success'
                element={<SimpleLoginSuccess />}
              />

              {/* Email verification check */}
              <Route
                path='/check-email'
                element={<EmailVerificationCheck />}
              />

              {/* Job-related routes */}
              <Route path='/jobs/:id' element={<AppleJobDetailPage />} />
              <Route
                path='/jobs/create'
                element={
                  <ProtectedRoute>
                    <AppleJobCreatePage />
                  </ProtectedRoute>
                }
              />
              <Route
                path='/jobs/:id/apply'
                element={
                  <ProtectedRoute>
                    <AppleJobApplyPage />
                  </ProtectedRoute>
                }
              />

              {/* Protected Routes - require authentication */}
              <Route
                path='/dashboard'
                element={
                  <ProtectedRoute>
                    <AppleDashboard />
                  </ProtectedRoute>
                }
              />
              <Route
                path='/messages'
                element={
                  <ProtectedRoute>
                    <MessagesPage />
                  </ProtectedRoute>
                }
              />
              <Route
                path='/settings'
                element={
                  <ProtectedRoute>
                    <SettingsPage />
                  </ProtectedRoute>
                }
              />

              {/* 404 Page */}
              <Route path='*' element={<NotFoundPage />} />
            </Routes>
          </ProfileGuard>
        </Layout>

        {/* Email Verification Modal */}
        <EmailVerificationModal
          isOpen={showEmailVerification}
          onClose={handleContinueVerification}
          email={verificationEmail}
          onResendVerification={handleResendVerification}
          onContinue={handleContinueVerification}
        />

        {/* Global Toast Notifications */}
        <Toaster
          position='top-right'
          toastOptions={{
            duration: 4000,
            style: {
              background: 'var(--toast-bg)',
              color: 'var(--toast-color)',
              border: '1px solid var(--toast-border)',
              borderRadius: '12px',
              padding: '16px',
              fontSize: '14px',
              fontWeight: '500',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#ffffff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#ffffff',
              },
            },
          }}
        />

        {/* Debug component for development only */}
        {process.env.NODE_ENV === 'development' && <DebugComponent />}
      </Router>
    </>
  );
};

// Main App component
const App = () => {
  useEffect(() => {
    // Initialize performance monitoring
    performanceMonitor.startMonitoring();
    
    // Debug routing issues
    console.log('🔍 VWork App Starting - Debugging routing...');
    debugRouting();
    
    // Check create account button functionality
    setTimeout(() => {
      checkCreateAccountButton();
    }, 2000); // Wait for components to render
  }, []);

  return (
    <ThemeProvider>
      <LanguageProvider>
        <AuthProvider>
          <AppContent />
        </AuthProvider>
      </LanguageProvider>
    </ThemeProvider>
  );
};

export default App;
