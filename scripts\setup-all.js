#!/usr/bin/env node

const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');

const isWindows = os.platform() === 'win32';

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message, color = 'reset') => {
  console.log(`${colors[color]}[SETUP] ${message}${colors.reset}`);
};

// Run command utility
function runCommand(command, cwd, description) {
  return new Promise((resolve, reject) => {
    log(`Running: ${description}`, 'cyan');
    
    const childProcess = exec(command, { 
      cwd,
      stdio: 'inherit',
      shell: true
    }, (error) => {
      if (error) {
        log(`Error in ${description}: ${error.message}`, 'red');
        reject(error);
      } else {
        log(`✅ Completed: ${description}`, 'green');
        resolve();
      }
    });
    
    childProcess.stdout.on('data', (data) => {
      process.stdout.write(data);
    });
    
    childProcess.stderr.on('data', (data) => {
      process.stderr.write(data);
    });
  });
}

// Check if directory exists
function checkDirectory(dirPath, name) {
  if (!fs.existsSync(dirPath)) {
    log(`❌ ${name} directory not found: ${dirPath}`, 'red');
    return false;
  }
  return true;
}

// Setup environment files
async function setupEnvironmentFiles() {
  log('Setting up environment files...', 'yellow');
  
  const rootEnvPath = path.join(__dirname, '..', '.env');
  const clientDir = path.join(__dirname, '..', 'client');
  const servicesDir = path.join(__dirname, '..', 'services');
  
  // Check if root .env exists
  if (!fs.existsSync(rootEnvPath)) {
    log('⚠️  No .env file found in root directory', 'yellow');
    log('Creating template .env file...', 'cyan');
    
    const templateEnv = `# VWork Platform Environment Configuration
# Copy this file to .env and configure your settings

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=vwork_db
DB_USER=vwork_user
DB_PASSWORD=vwork_password

# Firebase Configuration
FIREBASE_API_KEY=your_firebase_api_key
FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_STORAGE_BUCKET=your_project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=123456789
FIREBASE_APP_ID=your_app_id

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Payment Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# AWS Configuration (optional)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket

# Development Configuration
NODE_ENV=development
PORT=3000
CORS_ORIGIN=http://localhost:3000

# Client Configuration
REACT_APP_API_URL=http://localhost:8080
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=your_app_id
`;
    
    fs.writeFileSync(rootEnvPath, templateEnv);
    log('✅ Created template .env file', 'green');
    log('⚠️  Please configure your .env file with your actual settings', 'yellow');
  } else {
    log('✅ Root .env file found', 'green');
  }
  
  // Create client .env file
  if (checkDirectory(clientDir, 'Client')) {
    const clientEnvPath = path.join(clientDir, '.env');
    let clientEnvContent = `# Client Environment Configuration
PORT=3000
REACT_APP_API_URL=http://localhost:8080
NODE_ENV=development
`;

    if (fs.existsSync(rootEnvPath)) {
      const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
      const clientEnvLines = rootEnvContent.split('\n')
        .filter(line => {
          const trimmed = line.trim();
          return trimmed.startsWith('REACT_APP_') || 
                 trimmed.startsWith('NODE_ENV') ||
                 trimmed.startsWith('#') ||
                 trimmed === '';
        });
      clientEnvContent += '\n' + clientEnvLines.join('\n');
    }

    fs.writeFileSync(clientEnvPath, clientEnvContent);
    log('✅ Client .env file created', 'green');
  }

  // Create services .env files
  const services = [
    { name: 'auth-service', port: 3001 },
    { name: 'user-service', port: 3002 },
    { name: 'project-service', port: 3003 },
    { name: 'payment-service', port: 3004 },
    { name: 'chat-service', port: 3005 },
    { name: 'api-gateway', port: 8080 }
  ];

  for (const service of services) {
    const serviceDir = path.join(servicesDir, service.name);
    const serviceEnvPath = path.join(serviceDir, '.env');
    
    if (checkDirectory(serviceDir, service.name)) {
      let serviceEnvContent = `# ${service.name} Environment Configuration
NODE_ENV=development
PORT=${service.port}
CORS_ORIGIN=http://localhost:3000
`;
      
      if (fs.existsSync(rootEnvPath)) {
        const rootEnvContent = fs.readFileSync(rootEnvPath, 'utf8');
        serviceEnvContent += '\n' + rootEnvContent;
      }
      
      fs.writeFileSync(serviceEnvPath, serviceEnvContent);
      log(`✅ ${service.name} .env file created`, 'green');
    }
  }
}

// Install dependencies for a directory
async function installDependencies(dirPath, name) {
  if (!checkDirectory(dirPath, name)) {
    return;
  }
  
  const packageJsonPath = path.join(dirPath, 'package.json');
  if (!fs.existsSync(packageJsonPath)) {
    log(`⚠️  No package.json found in ${name}`, 'yellow');
    return;
  }
  
  const nodeModulesPath = path.join(dirPath, 'node_modules');
  
  try {
    // Always install dependencies to ensure everything is up to date
    if (fs.existsSync(nodeModulesPath)) {
      log(`📦 Reinstalling dependencies for ${name}`, 'blue');
    } else {
      log(`📦 Installing dependencies for ${name}`, 'blue');
    }
    
    await runCommand('npm install', dirPath, `Installing dependencies for ${name}`);
    
    // Special handling for client to ensure Firebase is properly installed
    if (name === 'Client') {
      log(`🔧 Ensuring Firebase dependencies for ${name}`, 'cyan');
      await runCommand('npm install firebase@^11.9.1', dirPath, `Installing Firebase for ${name}`);
    }
  } catch (error) {
    log(`❌ Failed to install dependencies for ${name}`, 'red');
    throw error;
  }
}

// Install all dependencies
async function installAllDependencies() {
  log('Installing all dependencies...', 'yellow');
  
  const directories = [
    { name: 'Root', path: path.join(__dirname, '..') },
    { name: 'Client', path: path.join(__dirname, '..', 'client') },
    { name: 'Auth Service', path: path.join(__dirname, '..', 'services', 'auth-service') },
    { name: 'User Service', path: path.join(__dirname, '..', 'services', 'user-service') },
    { name: 'Project Service', path: path.join(__dirname, '..', 'services', 'project-service') },
    { name: 'Payment Service', path: path.join(__dirname, '..', 'services', 'payment-service') },
    { name: 'Chat Service', path: path.join(__dirname, '..', 'services', 'chat-service') },
    { name: 'API Gateway', path: path.join(__dirname, '..', 'services', 'api-gateway') }
  ];

  for (const dir of directories) {
    await installDependencies(dir.path, dir.name);
  }
}

// Check Node.js and npm versions
function checkPrerequisites() {
  log('Checking prerequisites...', 'cyan');
  
  const nodeVersion = process.version;
  const npmVersion = require('child_process').execSync('npm --version', { encoding: 'utf8' }).trim();
  
  log(`Node.js version: ${nodeVersion}`, 'blue');
  log(`npm version: ${npmVersion}`, 'blue');
  
  // Check if Node.js version is >= 18
  const nodeMajor = parseInt(process.version.slice(1).split('.')[0]);
  if (nodeMajor < 18) {
    log('❌ Node.js version 18 or higher is required', 'red');
    process.exit(1);
  }
  
  log('✅ Prerequisites check passed', 'green');
}

// Create necessary directories
function createDirectories() {
  log('Creating necessary directories...', 'cyan');
  
  const directories = [
    'services/auth-service/uploads',
    'services/auth-service/uploads/avatars',
    'services/auth-service/uploads/temp',
    'services/user-service/uploads',
    'services/user-service/uploads/avatars',
    'services/user-service/uploads/temp',
    'services/project-service/uploads',
    'services/project-service/uploads/temp',
    'logs',
    'temp'
  ];
  
  directories.forEach(dir => {
    const fullPath = path.join(__dirname, '..', dir);
    if (!fs.existsSync(fullPath)) {
      fs.mkdirSync(fullPath, { recursive: true });
      log(`✅ Created directory: ${dir}`, 'green');
    }
  });
}

// Main setup function
async function setup() {
  try {
    log('🚀 Starting VWork Platform Setup...', 'bright');
    log('================================', 'bright');
    
    // Check prerequisites
    checkPrerequisites();
    
    // Create directories
    createDirectories();
    
    // Setup environment files
    await setupEnvironmentFiles();
    
    // Install all dependencies
    await installAllDependencies();
    
    log('================================', 'bright');
    log('🎉 Setup completed successfully!', 'green');
    log('================================', 'bright');
    log('', 'reset');
    log('📋 Next steps:', 'cyan');
    log('1. Configure your .env file with your actual settings', 'blue');
    log('2. Run "npm start" to start all services', 'blue');
    log('3. Run "npm stop" to stop all services', 'blue');
    log('', 'reset');
    log('🌐 Services will be available at:', 'cyan');
    log('• Client: http://localhost:3000', 'blue');
    log('• API Gateway: http://localhost:8080', 'blue');
    log('• Auth Service: http://localhost:3001', 'blue');
    log('• User Service: http://localhost:3002', 'blue');
    log('• Project Service: http://localhost:3003', 'blue');
    log('• Payment Service: http://localhost:3004', 'blue');
    log('• Chat Service: http://localhost:3005', 'blue');
    log('', 'reset');
    
  } catch (error) {
    log(`❌ Setup failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run setup
setup(); 