@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    VWork Platform Stop Script
echo ========================================
echo.

echo 🛑 Stopping all VWork processes...
echo.

REM Kill processes by port
echo 📡 Killing processes by port...
for %%p in (3000 3001 3002 3003 3004 3005 8080 5000 8000) do (
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :%%p') do (
        if not "%%a"=="0" (
            echo     Killing process on port %%p (PID: %%a)
            taskkill /F /PID %%a >nul 2>&1
        )
    )
)

echo.
echo 🔄 Killing development processes by name...
for %%p in (node.exe npm.exe npx.exe react-scripts) do (
    taskkill /F /IM %%p /T >nul 2>&1
)

echo.
echo 🖥️  Killing CMD windows...
for %%t in (VWork npm node react-scripts start dev client service) do (
    taskkill /F /FI "WINDOWTITLE eq *%%t*" >nul 2>&1
)

echo.
echo ✅ All processes stopped successfully!
echo 💡 You can now run "npm start" again.
echo.
pause 