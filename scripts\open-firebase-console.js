#!/usr/bin/env node

/**
 * Open Firebase Console for Domain Configuration
 */

const { spawn } = require('child_process');

console.log('🔥 Opening Firebase Console...\n');

const firebaseProjectId = 'vwork-786c3';
const authSettingsUrl = `https://console.firebase.google.com/project/${firebaseProjectId}/authentication/settings`;

console.log('🌐 Opening Firebase Authentication Settings:');
console.log(`   ${authSettingsUrl}`);

console.log('\n📋 What to do when it opens:');
console.log('1. Scroll down to "Authorized domains" section');
console.log('2. Click "Add domain" button');
console.log('3. Add: frontend-ce4z.onrender.com');
console.log('4. Add: *.onrender.com (for all Render subdomains)');
console.log('5. Click "Save"');

// Try to open the URL
try {
  let command;
  let args;
  
  if (process.platform === 'win32') {
    command = 'cmd';
    args = ['/c', 'start', authSettingsUrl];
  } else if (process.platform === 'darwin') {
    command = 'open';
    args = [authSettingsUrl];
  } else {
    command = 'xdg-open';
    args = [authSettingsUrl];
  }
  
  spawn(command, args, { detached: true, stdio: 'ignore' });
  console.log('\n✅ Firebase Console should open in your browser');
  
} catch (error) {
  console.log('\n❌ Could not auto-open browser');
  console.log('📋 Please manually open this URL:');
  console.log(`   ${authSettingsUrl}`);
}

console.log('\n🎯 Quick Steps:');
console.log('   Firebase Console → Authentication → Settings → Authorized domains → Add domain');
console.log('\n⏳ After adding domains, wait 1-2 minutes then test your app again!');
