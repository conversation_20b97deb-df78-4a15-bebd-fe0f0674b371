#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// <PERSON>h sách các port cần kill
const PORTS = [3000, 3001, 3002, 3003, 3004, 3005, 8080, 5000, 8000];

// <PERSON>h sách các process names cần kill
const PROCESS_NAMES = [
  'node.exe', 'npm.exe', 'npx.exe', 'react-scripts',
  'node', 'npm', 'npx'
];

// Danh sách các cmd window titles cần kill (cho Windows)
const CMD_TITLES = [
  'VWork', 'npm', 'node', 'react-scripts',
  'start', 'dev', 'client', 'service'
];

const isWindows = os.platform() === 'win32';

// Cross-platform command execution
function createCrossPlatformProcess(command, options = {}) {
  const execOptions = {
    timeout: options.timeout || 5000,
    env: { 
      ...process.env, 
      PATH: process.env.PATH,
      ...(isWindows && { PATHEXT: process.env.PATHEXT || '.COM;.EXE;.BAT;.CMD' })
    },
    shell: true,
    windowsHide: true
  };
  
  return exec(command, execOptions);
}

/**
 * Kill process theo port
 */
function killPort(port) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      console.log(`⏰ Timeout for port ${port}`);
      resolve();
    }, 5000);
    
    if (isWindows) {
      // Windows: Sử dụng netstat và taskkill
      exec(`netstat -ano | findstr :${port}`, { timeout: 3000 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout) {
          console.log(`❌ No process found on port ${port}`);
          resolve();
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.trim());
        const pids = lines.map(line => {
          const parts = line.trim().split(/\s+/);
          return parts[parts.length - 1];
        }).filter(pid => pid && pid !== '0');
        
        if (pids.length > 0) {
          const uniquePids = [...new Set(pids)];
          uniquePids.forEach(pid => {
            exec(`taskkill /F /T /PID ${pid}`, (killError) => {
              if (!killError) {
                console.log(`✅ Killed process ${pid} on port ${port}`);
              }
            });
          });
        }
        resolve();
      });
    } else {
      // Unix/Linux/macOS: Sử dụng lsof và kill
      exec(`lsof -ti:${port}`, { timeout: 3000 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout) {
          console.log(`❌ No process found on port ${port}`);
          resolve();
          return;
        }
        
        const pids = stdout.trim().split('\n').filter(pid => pid);
        pids.forEach(pid => {
          exec(`kill -9 ${pid}`, (killError) => {
            if (!killError) {
              console.log(`✅ Killed process ${pid} on port ${port}`);
            }
          });
        });
        resolve();
      });
    }
  });
}

/**
 * Kill processes by name
 */
function killProcessByName(processName) {
  return new Promise((resolve) => {
    const timeout = setTimeout(() => {
      resolve();
    }, 3000);
    
    if (isWindows) {
      exec(`tasklist /FI "IMAGENAME eq ${processName}" /FO CSV | findstr /V "INFO:"`, { timeout: 2000 }, (error, stdout) => {
        clearTimeout(timeout);
        if (!stdout || stdout.includes('INFO: No tasks')) {
          resolve();
          return;
        }
        
        exec(`taskkill /F /IM ${processName} /T`, (killError, killStdout) => {
          if (!killError) {
            console.log(`✅ Killed all ${processName} processes`);
          }
          resolve();
        });
      });
    } else {
      exec(`pkill -f ${processName}`, { timeout: 2000 }, (error) => {
        clearTimeout(timeout);
        if (!error) {
          console.log(`✅ Killed all ${processName} processes`);
        }
        resolve();
      });
    }
  });
}

/**
 * Kill cmd windows by title (Windows only)
 */
function killCmdWindows() {
  if (!isWindows) return Promise.resolve();
  
  return new Promise((resolve) => {
    console.log('🖥️  Killing CMD windows...');
    
    const promises = CMD_TITLES.map(title => {
      return new Promise((resolveTitle) => {
        exec(`tasklist /FI "WINDOWTITLE eq *${title}*" /FO CSV | findstr /V "INFO:"`, { timeout: 2000 }, (error, stdout) => {
          if (!stdout || stdout.includes('INFO: No tasks')) {
            resolveTitle();
            return;
          }
          
          exec(`taskkill /F /FI "WINDOWTITLE eq *${title}*"`, (killError) => {
            if (!killError) {
              console.log(`✅ Killed CMD windows with title containing "${title}"`);
            }
            resolveTitle();
          });
        });
      });
    });
    
    Promise.all(promises).then(resolve);
  });
}

/**
 * Kill processes from PID file
 */
function killProcessesFromFile() {
  const pidsFile = path.join(__dirname, '..', '.vwork-pids.json');
  
  if (fs.existsSync(pidsFile)) {
    try {
      const pids = JSON.parse(fs.readFileSync(pidsFile, 'utf8'));
      console.log('📄 Killing processes from PID file...');
      
      pids.forEach(pid => {
        if (isWindows) {
          exec(`taskkill /F /PID ${pid} 2>nul`, () => {});
        } else {
          exec(`kill -9 ${pid} 2>/dev/null`, () => {});
        }
      });
      
      // Remove PID file
      fs.unlinkSync(pidsFile);
      console.log('✅ Removed PID file');
    } catch (error) {
      console.log('⚠️  Error reading PID file:', error.message);
    }
  }
}

/**
 * Kill all VWork related processes
 */
function killAllVworkProcesses() {
  console.log('🔄 Killing all VWork processes...');
  
  // Kill by process name
  PROCESS_NAMES.forEach(processName => {
    if (isWindows) {
      exec(`taskkill /F /IM ${processName} /T 2>nul`, () => {});
    } else {
      exec(`pkill -f ${processName} 2>/dev/null`, () => {});
    }
  });
  
  // Kill by port
  PORTS.forEach(port => {
    if (isWindows) {
      exec(`netstat -ano | findstr :${port} | for /f "tokens=5" %a in ('findstr :${port}') do taskkill /F /PID %a 2>nul`, () => {});
    } else {
      exec(`lsof -ti:${port} | xargs kill -9 2>/dev/null`, () => {});
    }
  });
}

/**
 * Main function
 */
async function stopAllProcesses() {
  console.log('🛑 Stopping all VWork development processes...\n');
  
  // Kill processes from PID file first
  killProcessesFromFile();
  
  // Kill processes by port in parallel
  console.log('📡 Killing processes by port:');
  const portPromises = PORTS.map(port => killPort(port));
  await Promise.all(portPromises);
  
  console.log('\n🔄 Killing development processes by name:');
  
  // Kill processes by name
  const processPromises = PROCESS_NAMES.map(processName => killProcessByName(processName));
  await Promise.all(processPromises);
  
  // Kill CMD windows (Windows only)
  await killCmdWindows();
  
  // Kill all VWork processes as backup
  killAllVworkProcesses();
  
  // Small delay to ensure all operations complete
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  console.log('\n✅ All processes stopped successfully!');
  console.log('💡 You can now run "npm start" again.\n');
}

// Check for specific arguments
const args = process.argv.slice(2);

if (args.includes('--port') || args.includes('-p')) {
  const portIndex = args.findIndex(arg => arg === '--port' || arg === '-p');
  const port = parseInt(args[portIndex + 1]);
  
  if (port && !isNaN(port)) {
    console.log(`🛑 Stopping process on port ${port}...`);
    killPort(port).then(() => {
      console.log('✅ Done!');
    });
  } else {
    console.log('❌ Invalid port number');
    process.exit(1);
  }
} else if (args.includes('--force') || args.includes('-f')) {
  console.log('💥 Force killing all processes...');
  killAllVworkProcesses();
  killCmdWindows().then(() => {
    console.log('✅ Force kill completed!');
  });
} else {
  // Stop all processes
  stopAllProcesses().catch(console.error);
}
