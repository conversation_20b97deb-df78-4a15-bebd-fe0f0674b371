@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    VWork Platform Startup Script
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed or not in PATH
    pause
    exit /b 1
)

echo ✅ Node.js and npm are available
echo.

REM Kill any existing processes first
echo 🧹 Cleaning up existing processes...
call npm run stop >nul 2>&1

REM Wait a moment for processes to be killed
timeout /t 2 /nobreak >nul

echo.
echo 🚀 Starting VWork Platform...
echo.

REM Start all services
call npm start

echo.
echo ✅ VWork Platform started successfully!
echo.
echo 📱 Client: http://localhost:3000
echo 🔗 API Gateway: http://localhost:8080
echo.
echo 💡 Press Ctrl+C to stop all services
echo.
pause 