services:
  - type: web
    runtime: node
    name: vwork-gateway
    region: oregon
    branch: main
    rootDir: services/api-gateway
    buildCommand: npm ci
    startCommand: npm start
    healthCheckPath: /health
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: AUTH_SERVICE_URL
        value: https://vwork-auth-service.onrender.com
      - key: USER_SERVICE_URL
        value: https://vwork-user-service.onrender.com
      - key: PROJECT_SERVICE_URL
        value: https://vwork-project-service.onrender.com
      - key: JOB_SERVICE_URL
        value: https://vwork-job-service.onrender.com
      - key: CHAT_SERVICE_URL
        value: https://vwork-chat-service.onrender.com
      - key: SEARCH_SERVICE_URL
        value: https://vwork-search-service.onrender.com
      - key: CORS_ORIGIN
        value: https://frontend-ce4z.onrender.com,https://vwork-client.onrender.com
    plan: free
