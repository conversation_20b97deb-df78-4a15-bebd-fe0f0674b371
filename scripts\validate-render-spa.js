#!/usr/bin/env node

/**
 * Validate Render SPA Configuration
 */

const https = require('https');

const routes = [
  '/',
  '/auth',
  '/auth?mode=signup',
  '/auth?mode=login',
  '/login',
  '/register',
  '/login-success',
  '/dashboard',
  '/profile-setup',
  '/client-setup',
  '/verify-email',
  '/messages',
  '/settings',
  '/forgot-password',
  '/reset-password',
  '/check-email',
  '/simple-login-success',
  '/projects',
  '/projects/*',
  '/freelancers',
  '/freelancers/*',
  '/jobs',
  '/contests',
  '/community',
  '/support'
];

async function validateRoute(baseUrl, route) {
  return new Promise((resolve) => {
    const url = `${baseUrl}${route}`;
    console.log(`Testing: ${url}`);
    
    https.get(url, (res) => {
      const success = res.statusCode === 200;
      console.log(`  ${success ? '✅' : '❌'} ${res.statusCode} - ${url}`);
      resolve(success);
    }).on('error', (err) => {
      console.log(`  ❌ Error - ${url}: ${err.message}`);
      resolve(false);
    });
  });
}

async function validateDeployment(baseUrl) {
  console.log(`🧪 Validating SPA routes for: ${baseUrl}\n`);
  
  let successCount = 0;
  for (const route of routes) {
    const success = await validateRoute(baseUrl, route);
    if (success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 100)); // Rate limiting
  }
  
  console.log(`\n📊 Results: ${successCount}/${routes.length} routes working`);
  
  if (successCount === routes.length) {
    console.log('🎉 All routes are working correctly!');
  } else {
    console.log('⚠️  Some routes are not working. Check the deployment guide.');
  }
}

// Usage: node validate-render-spa.js https://your-app.onrender.com
const baseUrl = process.argv[2];
if (!baseUrl) {
  console.log('Usage: node validate-render-spa.js https://your-app.onrender.com');
  process.exit(1);
}

validateDeployment(baseUrl);
