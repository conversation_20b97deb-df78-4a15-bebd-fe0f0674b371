#!/usr/bin/env node

/**
 * Deploy to Render - Complete Setup Script
 * 
 * This script provides a comprehensive guide and automated checks for deploying
 * the VWork platform to Render.com with proper SPA routing configuration.
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 VWork Platform - Render Deployment Script\n');

// Check if all required files exist
const requiredFiles = [
  'render.yaml',
  'client/render.yaml',
  'client/.env.production',
  'backend.env.template',
  'RENDER_SPA_DEPLOYMENT_GUIDE.md',
  'scripts/validate-render-spa.js'
];

console.log('📋 Checking Required Files:');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(__dirname, '..', file);
  if (fs.existsSync(filePath)) {
    console.log(`  ✅ ${file}`);
  } else {
    console.log(`  ❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please run setup scripts first.');
  process.exit(1);
}

console.log('\n🎯 Deployment Checklist:\n');

const checklist = [
  {
    title: '1. Code Repository',
    items: [
      'All code changes committed to Git',
      'Repository pushed to GitHub',
      'render.yaml files are in place',
      'Environment variables configured'
    ]
  },
  {
    title: '2. Render Dashboard Setup',
    items: [
      'Render account created and logged in',
      'GitHub repository connected',
      'Ready to create services'
    ]
  },
  {
    title: '3. SPA Routing Configuration',
    items: [
      'Understand manual redirect rule setup required',
      'Know to use REWRITE not REDIRECT for /* → /index.html',
      'Ready to configure in Render Dashboard'
    ]
  }
];

checklist.forEach(section => {
  console.log(`📌 ${section.title}:`);
  section.items.forEach(item => {
    console.log(`   □ ${item}`);
  });
  console.log('');
});

console.log('🔧 Environment Variables Ready:');
console.log('   ✅ Client: client/.env.production');
console.log('   ✅ Backend: backend.env.template');
console.log('   ✅ All Firebase credentials configured');
console.log('   ✅ JWT secret generated');

console.log('\n📖 Deployment Instructions:');
console.log('   📄 Complete guide: RENDER_SPA_DEPLOYMENT_GUIDE.md');
console.log('   🧪 Validation script: scripts/validate-render-spa.js');

console.log('\n🚨 CRITICAL: Manual Steps Required');
console.log('   ⚠️  Render requires manual configuration in Dashboard');
console.log('   ⚠️  Must configure redirect rules: /* → /index.html (REWRITE)');
console.log('   ⚠️  Must copy environment variables from generated files');

console.log('\n📋 Deployment Order:');
const deploymentOrder = [
  'Auth Service (vwork-auth-service)',
  'User Service (vwork-user-service)', 
  'Project Service (vwork-project-service)',
  'Job Service (vwork-job-service)',
  'Chat Service (vwork-chat-service)',
  'Search Service (vwork-search-service)',
  'API Gateway (vwork-gateway)',
  'React Client (vwork-client) - STATIC SITE'
];

deploymentOrder.forEach((service, index) => {
  console.log(`   ${index + 1}. ${service}`);
});

console.log('\n🎯 Expected URLs After Deployment:');
const expectedUrls = [
  'https://vwork-client.onrender.com (Main App)',
  'https://vwork-gateway.onrender.com (API Gateway)',
  'https://vwork-auth-service.onrender.com',
  'https://vwork-user-service.onrender.com',
  'https://vwork-project-service.onrender.com',
  'https://vwork-job-service.onrender.com',
  'https://vwork-chat-service.onrender.com',
  'https://vwork-search-service.onrender.com'
];

expectedUrls.forEach(url => {
  console.log(`   🌐 ${url}`);
});

console.log('\n🧪 Testing Commands:');
console.log('   # Test individual service health:');
console.log('   curl https://vwork-auth-service.onrender.com/health');
console.log('');
console.log('   # Test SPA routing:');
console.log('   node scripts/validate-render-spa.js https://vwork-client.onrender.com');
console.log('');
console.log('   # Test specific routes:');
console.log('   curl https://vwork-client.onrender.com/auth');
console.log('   curl https://vwork-client.onrender.com/auth?mode=signup');

console.log('\n🚨 Common Issues & Solutions:');
console.log('   ❌ 404 on /auth routes → Check redirect rules in Dashboard');
console.log('   ❌ Service won\'t start → Check environment variables');
console.log('   ❌ Build fails → Check Node.js version (use 18.x)');
console.log('   ❌ CORS errors → Check service URLs in env vars');

console.log('\n📞 Support Resources:');
console.log('   📖 Render Docs: https://render.com/docs/redirects-rewrites');
console.log('   📖 SPA Guide: RENDER_SPA_DEPLOYMENT_GUIDE.md');
console.log('   🧪 Validation: scripts/validate-render-spa.js');

console.log('\n🎉 Ready to Deploy!');
console.log('   1. Read RENDER_SPA_DEPLOYMENT_GUIDE.md');
console.log('   2. Follow manual deployment steps');
console.log('   3. Configure redirect rules in Dashboard');
console.log('   4. Test with validation script');

console.log('\n⏱️  Estimated Deployment Time: 30-45 minutes');
console.log('💡 Tip: Deploy services one by one and test each before proceeding');

// Create a quick deployment summary
const summaryContent = `# VWork Render Deployment Summary

## ✅ Pre-deployment Checklist Complete
- [x] All configuration files created
- [x] Environment variables configured  
- [x] SPA routing setup prepared
- [x] Validation scripts ready

## 🚀 Next Steps
1. Follow RENDER_SPA_DEPLOYMENT_GUIDE.md
2. Deploy services in order (Auth → User → Project → Job → Chat → Search → Gateway → Client)
3. Configure redirect rules in Render Dashboard
4. Test with: node scripts/validate-render-spa.js https://vwork-client.onrender.com

## 🚨 Critical Manual Steps
- Configure /* → /index.html as REWRITE in Render Dashboard
- Copy environment variables from client/.env.production and backend.env.template
- Test each service after deployment

## 📞 Support
- Read RENDER_SPA_DEPLOYMENT_GUIDE.md for detailed instructions
- Use scripts/validate-render-spa.js for testing
- Check Render logs if services fail to start

Generated: ${new Date().toISOString()}
`;

const summaryPath = path.join(__dirname, '..', 'DEPLOYMENT_SUMMARY.md');
fs.writeFileSync(summaryPath, summaryContent);
console.log('\n✅ Created DEPLOYMENT_SUMMARY.md');

console.log('\n🎯 All systems ready for Render deployment! 🚀');
