#!/usr/bin/env node

/**
 * Setup Render SPA Routing Configuration
 * 
 * This script helps configure Render.com for proper Single Page Application (SPA) routing.
 * It creates the necessary configuration files and provides instructions for manual setup.
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Setting up Render SPA Routing Configuration...\n');

// Define all routes that need to be handled by the SPA
const spaRoutes = [
  '/',
  '/auth',
  '/auth?mode=signup',
  '/auth?mode=login',
  '/login',
  '/register',
  '/login-success',
  '/dashboard',
  '/profile-setup',
  '/client-setup',
  '/verify-email',
  '/messages',
  '/settings',
  '/forgot-password',
  '/reset-password',
  '/check-email',
  '/simple-login-success',
  '/projects',
  '/projects/*',
  '/freelancers',
  '/freelancers/*',
  '/jobs',
  '/contests',
  '/community',
  '/support'
];

// Create a comprehensive _redirects file for backup
const redirectsContent = `# Handle client-side routing for all routes
/*    /index.html   200

# Specific routes for better performance and SEO
${spaRoutes.map(route => `${route}    /index.html   200`).join('\n')}

# API routes should not be redirected (if any static API routes exist)
/api/*    /api/:splat   200

# Static assets should be served directly
/static/*    /static/:splat   200
/images/*    /images/:splat   200
/css/*       /css/:splat      200
/js/*        /js/:splat       200
`;

// Write the _redirects file
const redirectsPath = path.join(__dirname, '..', 'client', 'public', '_redirects');
fs.writeFileSync(redirectsPath, redirectsContent);
console.log('✅ Updated client/public/_redirects file');

// Create a Render deployment guide
const deploymentGuide = `# Render.com SPA Deployment Guide

## 🚨 IMPORTANT: Manual Configuration Required

Render.com requires manual configuration in the dashboard for SPA routing.
The render.yaml file contains the correct configuration, but you MUST also:

### 1. Configure Redirect Rules in Render Dashboard

Go to your static site settings in Render Dashboard and add these redirect rules:

**Rule 1: SPA Fallback**
- Source: \`/*\`
- Destination: \`/index.html\`
- Action: Rewrite (NOT Redirect)

### 2. Verify Environment Variables

Ensure these environment variables are set in Render Dashboard:

\`\`\`
NODE_ENV=production
GENERATE_SOURCEMAP=false
REACT_APP_API_BASE_URL=https://vwork-gateway.onrender.com/api/v1
REACT_APP_FIREBASE_API_KEY=AIzaSyBy8ymWrOGYwcjS-Ii4PgyzWLdb-A4U6nw
REACT_APP_FIREBASE_AUTH_DOMAIN=vwork-786c3.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=vwork-786c3
REACT_APP_FIREBASE_STORAGE_BUCKET=vwork-786c3.firebasestorage.app
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=*************
REACT_APP_FIREBASE_APP_ID=1:*************:web:dfeae89c9ba66c77aeec02
\`\`\`

### 3. Build Settings

Ensure these build settings in Render Dashboard:

- **Build Command:** \`npm ci && npm run build\`
- **Publish Directory:** \`build\`
- **Root Directory:** \`client\`

### 4. Test These Routes

After deployment, test these critical routes:

${spaRoutes.map(route => `- https://your-app.onrender.com${route}`).join('\n')}

### 5. Common Issues & Solutions

**404 Errors on Direct URL Access:**
- Ensure the rewrite rule (/*  →  /index.html) is configured as REWRITE, not REDIRECT
- Check that the rule is at the top of the rules list

**Environment Variables Not Working:**
- Verify all REACT_APP_ variables are set in Render Dashboard
- Redeploy after adding environment variables

**Build Failures:**
- Check that Node.js version is 18.x or higher
- Ensure package.json has correct dependencies

### 6. Deployment Checklist

- [ ] render.yaml file is in repository root
- [ ] client/render.yaml file is configured
- [ ] Redirect rules configured in Render Dashboard
- [ ] Environment variables set in Render Dashboard
- [ ] Build settings configured correctly
- [ ] Test deployment with direct URL access
- [ ] Verify all SPA routes work correctly

## 🔧 Troubleshooting

If you still get 404 errors:

1. Check Render Dashboard logs for build errors
2. Verify the rewrite rule is configured as "Rewrite" not "Redirect"
3. Ensure the rule source is exactly: \`/*\`
4. Ensure the rule destination is exactly: \`/index.html\`
5. Make sure the rule is the first rule in the list

## 📞 Support

If issues persist, check:
- Render documentation: https://render.com/docs/redirects-rewrites
- This project's GitHub issues
- Render community forum
`;

// Write the deployment guide
const guidePath = path.join(__dirname, '..', 'RENDER_SPA_DEPLOYMENT_GUIDE.md');
fs.writeFileSync(guidePath, deploymentGuide);
console.log('✅ Created RENDER_SPA_DEPLOYMENT_GUIDE.md');

// Create a validation script
const validationScript = `#!/usr/bin/env node

/**
 * Validate Render SPA Configuration
 */

const https = require('https');

const routes = [
${spaRoutes.map(route => `  '${route}'`).join(',\n')}
];

async function validateRoute(baseUrl, route) {
  return new Promise((resolve) => {
    const url = \`\${baseUrl}\${route}\`;
    console.log(\`Testing: \${url}\`);
    
    https.get(url, (res) => {
      const success = res.statusCode === 200;
      console.log(\`  \${success ? '✅' : '❌'} \${res.statusCode} - \${url}\`);
      resolve(success);
    }).on('error', (err) => {
      console.log(\`  ❌ Error - \${url}: \${err.message}\`);
      resolve(false);
    });
  });
}

async function validateDeployment(baseUrl) {
  console.log(\`🧪 Validating SPA routes for: \${baseUrl}\\n\`);
  
  let successCount = 0;
  for (const route of routes) {
    const success = await validateRoute(baseUrl, route);
    if (success) successCount++;
    await new Promise(resolve => setTimeout(resolve, 100)); // Rate limiting
  }
  
  console.log(\`\\n📊 Results: \${successCount}/\${routes.length} routes working\`);
  
  if (successCount === routes.length) {
    console.log('🎉 All routes are working correctly!');
  } else {
    console.log('⚠️  Some routes are not working. Check the deployment guide.');
  }
}

// Usage: node validate-render-spa.js https://your-app.onrender.com
const baseUrl = process.argv[2];
if (!baseUrl) {
  console.log('Usage: node validate-render-spa.js https://your-app.onrender.com');
  process.exit(1);
}

validateDeployment(baseUrl);
`;

// Write the validation script
const validationPath = path.join(__dirname, 'validate-render-spa.js');
fs.writeFileSync(validationPath, validationScript);
console.log('✅ Created scripts/validate-render-spa.js');

console.log('\n🎯 Setup Complete!');
console.log('\n📋 Next Steps:');
console.log('1. Read RENDER_SPA_DEPLOYMENT_GUIDE.md for manual configuration steps');
console.log('2. Configure redirect rules in Render Dashboard');
console.log('3. Deploy your application');
console.log('4. Test with: node scripts/validate-render-spa.js https://your-app.onrender.com');
console.log('\n⚠️  IMPORTANT: Manual configuration in Render Dashboard is required!');
